<template>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
    <!-- 总订单数 -->
    <MagicCard class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">总订单数</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">
            {{ stats.total || 0 }}
          </p>
          <p class="text-xs text-gray-500 mt-1">
            <span class="text-green-600">+{{ stats.todayNew || 0 }}</span> 今日新增
          </p>
        </div>
        <div class="p-3 bg-blue-100 rounded-full dark:bg-blue-900/30">
          <el-icon class="text-blue-600 text-xl">
            <Box />
          </el-icon>
        </div>
      </div>
    </MagicCard>

    <!-- 待处理订单 -->
    <MagicCard class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">待处理</p>
          <p class="text-2xl font-bold text-orange-600">
            {{ stats.pending || 0 }}
          </p>
          <p class="text-xs text-gray-500 mt-1">
            需要关注的订单
          </p>
        </div>
        <div class="p-3 bg-orange-100 rounded-full dark:bg-orange-900/30">
          <el-icon class="text-orange-600 text-xl">
            <Clock />
          </el-icon>
        </div>
      </div>
    </MagicCard>

    <!-- 运输中订单 -->
    <MagicCard class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">运输中</p>
          <p class="text-2xl font-bold text-purple-600">
            {{ stats.shipping || 0 }}
          </p>
          <p class="text-xs text-gray-500 mt-1">
            正在配送中
          </p>
        </div>
        <div class="p-3 bg-purple-100 rounded-full dark:bg-purple-900/30">
          <el-icon class="text-purple-600 text-xl">
            <Van />
          </el-icon>
        </div>
      </div>
    </MagicCard>

    <!-- 已完成订单 -->
    <MagicCard class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">已完成</p>
          <p class="text-2xl font-bold text-green-600">
            {{ stats.completed || 0 }}
          </p>
          <p class="text-xs text-gray-500 mt-1">
            <span class="text-green-600">{{ completionRate }}%</span> 完成率
          </p>
        </div>
        <div class="p-3 bg-green-100 rounded-full dark:bg-green-900/30">
          <el-icon class="text-green-600 text-xl">
            <Check />
          </el-icon>
        </div>
      </div>
    </MagicCard>
  </div>

  <!-- 趋势图表 -->
  <MagicCard class="p-6 mb-6" v-if="showChart">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">订单趋势</h3>
      <el-button-group size="small">
        <el-button 
          :type="chartPeriod === '7d' ? 'primary' : 'default'"
          @click="chartPeriod = '7d'"
        >
          7天
        </el-button>
        <el-button 
          :type="chartPeriod === '30d' ? 'primary' : 'default'"
          @click="chartPeriod = '30d'"
        >
          30天
        </el-button>
      </el-button-group>
    </div>
    
    <!-- 简单的趋势指示器 -->
    <div class="grid grid-cols-7 gap-2 h-20">
      <div 
        v-for="(day, index) in trendData" 
        :key="index"
        class="flex flex-col items-center justify-end"
      >
        <div 
          class="w-full bg-blue-200 rounded-t transition-all duration-300 hover:bg-blue-300"
          :style="{ height: `${(day.count / maxTrendValue) * 100}%` }"
        />
        <span class="text-xs text-gray-500 mt-1">{{ day.label }}</span>
      </div>
    </div>
  </MagicCard>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import MagicCard from '@/components/ui/magic-card.vue'
import { Box, Clock, Van, Check } from '@element-plus/icons-vue'

interface OrderStats {
  total?: number
  pending?: number
  shipping?: number
  completed?: number
  todayNew?: number
}

interface TrendData {
  label: string
  count: number
}

interface Props {
  stats: OrderStats
  showChart?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showChart: true
})

const chartPeriod = ref<'7d' | '30d'>('7d')

// 计算完成率
const completionRate = computed(() => {
  const total = props.stats.total || 0
  const completed = props.stats.completed || 0
  if (total === 0) return 0
  return Math.round((completed / total) * 100)
})

// 模拟趋势数据
const trendData = computed<TrendData[]>(() => {
  const days = chartPeriod.value === '7d' ? 7 : 30
  const data: TrendData[] = []
  
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    
    data.push({
      label: chartPeriod.value === '7d' 
        ? date.toLocaleDateString('zh-CN', { weekday: 'short' })
        : date.getDate().toString(),
      count: Math.floor(Math.random() * 50) + 10 // 模拟数据
    })
  }
  
  return data
})

const maxTrendValue = computed(() => {
  return Math.max(...trendData.value.map(d => d.count))
})
</script>
